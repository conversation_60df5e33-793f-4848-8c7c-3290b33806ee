import os
import uuid
from pathlib import Path

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request

app = FastAPI(title="QR Code Verification Service")

# 配置模板
templates = Jinja2Templates(directory="app/templates")

# 静态文件目录
STATIC_IMAGES_DIR = "/app/static/images"

def is_valid_uuid(uuid_string: str) -> bool:
    """验证UUID格式"""
    try:
        uuid.UUID(uuid_string)
        return True
    except ValueError:
        return False

def image_exists(image_id: str) -> bool:
    """检查图片是否存在"""
    image_path = Path(STATIC_IMAGES_DIR) / f"{image_id}.jpg"
    return image_path.exists()

@app.get("/{image_id}")
async def verify_image(request: Request, image_id: str):
    """验证并显示图片"""
    
    # 验证UUID格式
    if not is_valid_uuid(image_id):
        raise HTTPException(status_code=404, detail="Invalid image ID format")
    
    # 检查图片是否存在
    if not image_exists(image_id):
        raise HTTPException(status_code=404, detail="Image not found")
    
    # 返回图片展示页面
    return templates.TemplateResponse("view.html", {
        "request": request,
        "image_id": image_id
    })

@app.get("/image/{image_id}")
async def get_image(image_id: str):
    """获取图片文件"""
    
    # 验证UUID格式
    if not is_valid_uuid(image_id):
        raise HTTPException(status_code=404, detail="Invalid image ID format")
    
    # 构建图片路径
    image_path = Path(STATIC_IMAGES_DIR) / f"{image_id}.jpg"
    
    # 检查图片是否存在
    if not image_path.exists():
        raise HTTPException(status_code=404, detail="Image not found")
    
    # 返回图片文件
    return FileResponse(
        image_path,
        media_type="image/jpeg",
        headers={"Cache-Control": "public, max-age=3600"}  # 缓存1小时
    )

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "verification"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=80)
