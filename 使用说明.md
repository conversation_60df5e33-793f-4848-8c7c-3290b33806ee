# 二维码添加与验证网页服务 - 使用说明

## 项目概述

这是一个基于Docker的二维码添加和扫码验证网页服务，支持图片上传、二维码添加、批量处理和扫码验证功能。

## 功能特性

- ✅ 图片上传（单张/批量）
- ✅ 自动在图片左下角添加二维码
- ✅ 实时预览功能
- ✅ 二维码位置和尺寸自定义
- ✅ 批量处理和ZIP打包下载
- ✅ 二维码扫描验证
- ✅ 独立的上传和验证服务
- ✅ Docker容器化部署

## 快速开始

### 1. 启动服务

```bash
# 构建并启动服务
docker compose up -d

# 查看服务状态
docker compose ps
```

### 2. 访问服务

- **上传页面**: http://localhost:8080
- **验证服务**: http://localhost:8081/{image_id}

### 3. 使用流程

1. 打开上传页面 (http://localhost:8080)
2. 选择要处理的图片文件（支持单张或多张）
3. 调整二维码参数：
   - 左边距：距离图片左边缘的像素值
   - 下边距：距离图片下边缘的像素值
   - 二维码尺寸：相对于图片宽度的比例（0.01-1.0）
4. 实时预览二维码位置和尺寸
5. 点击"开始处理"按钮
6. 下载处理后的图片（单张直接下载，多张打包为ZIP）
7. 扫描图片上的二维码即可验证

## 技术架构

### 服务架构
```
┌─────────────────┐    ┌─────────────────┐
│   上传服务       │    │   验证服务       │
│   (Port 8080)   │    │   (Port 8081)   │
│   FastAPI       │    │   FastAPI       │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
            ┌─────────────────┐
            │   共享存储       │
            │   ./data/       │
            └─────────────────┘
```

### 技术栈
- **后端**: FastAPI + Python 3.11
- **图片处理**: Pillow (PIL)
- **二维码生成**: qrcode
- **前端**: HTML + CSS + JavaScript
- **容器化**: Docker + Docker Compose

### 目录结构
```
qr-code-service/
├── docker-compose.yml          # Docker编排文件
├── data/                       # 数据存储目录
│   └── processed/              # 处理后的图片
├── upload-service/             # 上传服务
│   ├── Dockerfile
│   ├── requirements.txt
│   └── app/
│       ├── main.py            # 主应用
│       └── templates/
│           └── upload.html    # 上传页面
└── verify-service/            # 验证服务
    ├── Dockerfile
    ├── requirements.txt
    └── app/
        ├── main.py           # 主应用
        └── templates/
            └── view.html     # 验证页面
```

## API文档

### 上传服务 (Port 8080)

#### GET /
- **功能**: 上传页面
- **返回**: HTML页面

#### POST /api/generate
- **功能**: 处理图片并添加二维码
- **参数**:
  - `files`: 图片文件列表
  - `marginLeft`: 左边距 (px)
  - `marginBottom`: 下边距 (px)
  - `qrSizeRatio`: 二维码尺寸比例 (0.01-1.0)
- **返回**: 处理后的图片文件或ZIP压缩包

### 验证服务 (Port 8081)

#### GET /{image_id}
- **功能**: 显示验证图片
- **参数**: `image_id` - 图片UUID
- **返回**: 图片展示页面

#### GET /image/{image_id}
- **功能**: 获取图片文件
- **参数**: `image_id` - 图片UUID
- **返回**: JPEG图片文件

#### GET /health
- **功能**: 健康检查
- **返回**: 服务状态

## 配置说明

### 环境变量

- `VERIFY_SERVICE_BASE_URL`: 验证服务的基础URL（默认: http://localhost:8081）

### 默认配置

- 支持的图片格式：JPG, PNG, WEBP, BMP, TIFF, GIF
- 输出格式：JPG (质量95)
- 默认左边距：20px
- 默认下边距：20px
- 默认二维码尺寸比例：0.15 (15%)

## 常见问题

### Q: 如何修改服务端口？
A: 编辑 `docker-compose.yml` 文件中的端口映射：
```yaml
ports:
  - "新端口:80"
```

### Q: 如何备份处理的图片？
A: 图片存储在 `./data/processed/` 目录中，直接备份该目录即可。

### Q: 如何查看服务日志？
A: 使用命令：`docker compose logs`

### Q: 服务无法启动怎么办？
A: 
1. 检查端口是否被占用
2. 查看日志：`docker compose logs`
3. 重新构建：`docker compose build --no-cache`

## 停止服务

```bash
# 停止服务
docker compose down

# 停止并删除数据卷
docker compose down -v
```

## 开发说明

如需修改代码，请：

1. 修改相应的源文件
2. 重新构建镜像：`docker compose build`
3. 重启服务：`docker compose up -d`

## 许可证

本项目基于您的需求开发，请根据实际情况添加适当的许可证。
