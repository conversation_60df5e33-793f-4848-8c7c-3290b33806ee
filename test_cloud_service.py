#!/usr/bin/env python3
"""
云端二维码服务测试脚本
测试通过frp穿透和反向代理后的服务功能
"""

import requests
import json
import time
from PIL import Image, ImageDraw
import io
import os
import qrcode
from pyzbar import pyzbar

def create_test_image(width=800, height=600, color='lightblue', text='Cloud Test Image'):
    """创建测试图片"""
    img = Image.new('RGB', (width, height), color)
    draw = ImageDraw.Draw(img)
    
    # 添加文字
    text_bbox = draw.textbbox((0, 0), text)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    draw.text((x, y), text, fill='black')
    
    return img

def test_local_upload_service():
    """测试本地上传服务"""
    print("🧪 测试本地上传服务...")
    
    # 创建测试图片
    test_img = create_test_image()
    
    # 将图片转换为字节流
    img_bytes = io.BytesIO()
    test_img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    # 准备上传数据
    files = {
        'files': ('cloud_test_image.png', img_bytes, 'image/png')
    }
    
    data = {
        'marginLeft': 40,
        'marginBottom': 40,
        'qrSizeRatio': 0.25
    }
    
    try:
        # 发送请求到本地服务
        response = requests.post(
            'http://localhost:8080/api/generate',
            files=files,
            data=data,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 本地上传服务测试成功")
            
            # 保存处理后的图片
            with open('cloud_test_output.jpg', 'wb') as f:
                f.write(response.content)
            print("📁 处理后的图片已保存为 cloud_test_output.jpg")
            
            return True
        else:
            print(f"❌ 本地上传服务测试失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 本地上传服务连接失败: {e}")
        return False

def test_cloud_upload_service():
    """测试云端上传服务"""
    print("🧪 测试云端上传服务...")
    
    # 创建测试图片
    test_img = create_test_image(text='Cloud Upload Test')
    
    # 将图片转换为字节流
    img_bytes = io.BytesIO()
    test_img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    # 准备上传数据
    files = {
        'files': ('cloud_upload_test.png', img_bytes, 'image/png')
    }
    
    data = {
        'marginLeft': 50,
        'marginBottom': 50,
        'qrSizeRatio': 0.2
    }
    
    try:
        # 发送请求到云端服务
        response = requests.post(
            'https://qr.gaoliming.top/api/generate',
            files=files,
            data=data,
            timeout=60
        )
        
        if response.status_code == 200:
            print("✅ 云端上传服务测试成功")
            
            # 保存处理后的图片
            with open('cloud_upload_output.jpg', 'wb') as f:
                f.write(response.content)
            print("📁 云端处理后的图片已保存为 cloud_upload_output.jpg")
            
            return True
        else:
            print(f"❌ 云端上传服务测试失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 云端上传服务连接失败: {e}")
        return False

def test_cloud_verification_service():
    """测试云端验证服务"""
    print("🧪 测试云端验证服务...")
    
    try:
        # 测试云端验证服务的根路径
        response = requests.get('https://qrverification.nanye.site/', timeout=10)
        
        if response.status_code == 404:
            print("✅ 云端验证服务运行正常 (404是预期的，因为没有提供image_id)")
        elif response.status_code == 200:
            print("✅ 云端验证服务运行正常")
        else:
            print(f"⚠️  云端验证服务响应: {response.status_code}")
        
        return True
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 云端验证服务连接失败: {e}")
        return False

def extract_qr_code_from_image(image_path):
    """从图片中提取二维码内容"""
    try:
        # 打开图片
        img = Image.open(image_path)
        
        # 解码二维码
        decoded_objects = pyzbar.decode(img)
        
        if decoded_objects:
            for obj in decoded_objects:
                qr_data = obj.data.decode('utf-8')
                print(f"📱 检测到二维码内容: {qr_data}")
                return qr_data
        else:
            print("❌ 未在图片中检测到二维码")
            return None
            
    except Exception as e:
        print(f"❌ 二维码提取失败: {e}")
        return None

def test_qr_code_verification(qr_url):
    """测试二维码验证链接"""
    print(f"🧪 测试二维码验证链接: {qr_url}")
    
    try:
        response = requests.get(qr_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ 二维码验证链接可正常访问")
            return True
        else:
            print(f"❌ 二维码验证链接访问失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 二维码验证链接连接失败: {e}")
        return False

def check_services_status():
    """检查所有服务状态"""
    print("🔍 检查服务状态...")
    
    services = [
        ('本地上传服务', 'http://localhost:8080/'),
        ('本地验证服务', 'http://localhost:8081/'),
        ('云端上传服务', 'https://qr.gaoliming.top/'),
        ('云端验证服务', 'https://qrverification.nanye.site/')
    ]
    
    for name, url in services:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code in [200, 404, 405]:  # 这些都是正常状态
                print(f"✅ {name} 运行正常")
            else:
                print(f"⚠️  {name} 状态: {response.status_code}")
        except requests.exceptions.RequestException:
            print(f"❌ {name} 无法访问")

def main():
    """主测试函数"""
    print("🚀 开始测试云端二维码服务...")
    print("=" * 60)
    
    # 检查服务状态
    check_services_status()
    
    print("\n" + "=" * 60)
    
    # 测试本地上传服务
    local_success = test_local_upload_service()
    
    print("\n" + "=" * 60)
    
    # 测试云端上传服务
    cloud_success = test_cloud_upload_service()
    
    print("\n" + "=" * 60)
    
    # 测试云端验证服务
    test_cloud_verification_service()
    
    print("\n" + "=" * 60)
    
    # 如果有生成的图片，尝试提取二维码
    test_files = ['cloud_test_output.jpg', 'cloud_upload_output.jpg']
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n🔍 分析 {test_file} 中的二维码...")
            qr_url = extract_qr_code_from_image(test_file)
            
            if qr_url:
                # 测试二维码链接
                test_qr_code_verification(qr_url)
    
    print("\n" + "=" * 60)
    print("🎉 云端服务测试完成！")
    
    if local_success or cloud_success:
        print("\n📋 测试结果:")
        print("   - 服务配置已更新为云端地址")
        print("   - 二维码现在指向: https://qrverification.nanye.site")
        print("   - 可以通过手机扫描二维码测试完整流程")

if __name__ == "__main__":
    main()
