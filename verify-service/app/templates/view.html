<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>上海顺耕信息技术有限公司</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: auto;
            background: #000;
            /* 禁止缩放和选择 */
            touch-action: pan-x pan-y;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .image-container {
            width: 100vw;
            height: 100vh;
            overflow: auto;
            position: relative;
        }

        .verified-image {
            display: block;
            min-width: 200vw;
            min-height: 150vh;
            object-fit: cover;
            /* 禁止图片缩放和拖拽 */
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;
            user-drag: none;
            pointer-events: none;
        }

        .loading {
            color: white;
            font-size: 1.2em;
            text-align: center;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }

        .error {
            color: #ff6b6b;
            font-size: 1.2em;
            text-align: center;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            z-index: 10;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .verified-image {
                min-width: 250vw;
                min-height: 200vh;
            }
        }

        @media (max-width: 480px) {
            .verified-image {
                min-width: 300vw;
                min-height: 250vh;
            }
        }

        /* 图片加载动画 */
        .verified-image {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .verified-image.loaded {
            opacity: 1;
        }
    </style>
</head>

<body>
    <div class="image-container">
        <div class="loading" id="loading">正在进行验证...</div>
        <img id="verifiedImage" class="verified-image" style="display: none;" alt="验证图片">
        <div class="error" id="error" style="display: none;">图片加载失败</div>
    </div>

    <script>
        const imageId = "{{ image_id }}";
        const loadingElement = document.getElementById('loading');
        const imageElement = document.getElementById('verifiedImage');
        const errorElement = document.getElementById('error');

        // 加载图片
        function loadImage() {
            const imageUrl = `/image/${imageId}`;

            imageElement.onload = function () {
                loadingElement.style.display = 'none';
                imageElement.style.display = 'block';
                imageElement.classList.add('loaded');
            };

            imageElement.onerror = function () {
                loadingElement.style.display = 'none';
                errorElement.style.display = 'block';
            };

            imageElement.src = imageUrl;
        }

        // 页面加载完成后开始加载图片
        document.addEventListener('DOMContentLoaded', loadImage);

        // 禁止各种缩放和交互
        document.addEventListener('contextmenu', function (e) {
            e.preventDefault();
        });

        document.addEventListener('dragstart', function (e) {
            e.preventDefault();
        });

        // 禁止双击缩放
        document.addEventListener('dblclick', function (e) {
            e.preventDefault();
        });

        // 禁止捏合缩放
        document.addEventListener('touchstart', function (e) {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        });

        document.addEventListener('touchmove', function (e) {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        });

        // 禁止鼠标滚轮缩放
        document.addEventListener('wheel', function (e) {
            if (e.ctrlKey) {
                e.preventDefault();
            }
        }, { passive: false });

        // 禁止键盘缩放
        document.addEventListener('keydown', function (e) {
            if ((e.ctrlKey || e.metaKey) && (e.key === '+' || e.key === '-' || e.key === '0')) {
                e.preventDefault();
            }
        });
    </script>
</body>

</html>