# 二维码添加与验证网页服务

一个基于Docker的二维码添加和扫码验证网页服务，支持图片上传、二维码添加、批量处理和扫码验证功能。

## ✨ 功能特性

- 🖼️ **图片上传**: 支持单张/批量上传多种格式图片
- 🔲 **二维码添加**: 自动在图片左下角添加纯黑色二维码
- 👁️ **实时预览**: 调整参数时可实时预览二维码位置和尺寸
- ⚙️ **参数自定义**: 可设置二维码距离边缘的距离和尺寸比例
- 📦 **批量处理**: 支持多文件上传和ZIP打包下载
- 📱 **扫码验证**: 扫描二维码可打开验证页面查看原图
- 🐳 **Docker部署**: 完整的容器化解决方案
- 🔄 **服务独立**: 上传服务和验证服务独立运行

## 🚀 快速开始

### 启动服务
```bash
docker compose up -d
```

### 访问服务
- **云端上传页面**: https://qr.gaoliming.top
- **本地上传页面**: http://localhost:8080
- **验证服务**: https://qrverification.nanye.site/{image_id}

## 📁 项目结构

```
qr-code-service/
├── docker-compose.yml          # 生产环境配置
├── docker-compose.dev.yml      # 开发环境配置
├── docker-compose.prod.yml     # 生产环境配置(明确标识)
├── data/                       # 数据存储目录
│   └── processed/              # 处理后的图片
├── upload-service/             # 上传服务
│   ├── Dockerfile
│   ├── requirements.txt
│   └── app/
│       ├── main.py            # 上传服务主程序
│       └── templates/
│           └── upload.html    # 上传页面(含实时预览)
└── verify-service/            # 验证服务
    ├── Dockerfile
    ├── requirements.txt
    └── app/
        ├── main.py           # 验证服务主程序
        └── templates/
            └── view.html     # 验证页面
```

## 🔧 部署方式

### 生产环境（云端）
```bash
docker compose up -d
# 或
docker compose -f docker-compose.prod.yml up -d
```

### 开发环境（本地）
```bash
docker compose -f docker-compose.dev.yml up -d
```

## 📖 详细文档

- [使用说明](使用说明.md) - 详细的使用指南和功能说明
- [部署指南](部署指南.md) - 完整的部署和运维指南
- [原始需求文档](原始需求文档.md) - 项目的原始需求和技术规格

## 🛠️ 技术栈

- **后端**: FastAPI + Python 3.11
- **图片处理**: Pillow (PIL)
- **二维码生成**: qrcode
- **前端**: HTML + CSS + JavaScript
- **容器化**: Docker + Docker Compose

## 🌐 云端部署

项目已配置云端部署，通过FRP内网穿透和反向代理实现：

- 上传服务: https://qr.gaoliming.top
- 验证服务: https://qrverification.nanye.site

## 📝 使用流程

1. 访问上传页面
2. 选择图片文件（支持拖拽）
3. 调整二维码参数（实时预览）
4. 处理并下载图片
5. 扫描二维码验证

## 🔒 安全特性

- HTTPS加密传输
- 文件类型验证
- 容器隔离
- UUID唯一标识

## 📊 性能特点

- 单张图片处理时间 < 2秒
- 支持多用户并发
- 高质量JPG输出
- 内存优化处理

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

请根据实际情况添加适当的许可证。
