# 二维码服务部署指南

## 🌐 云端部署配置

### 当前云端配置

您已经成功配置了以下云端服务：

#### FRP内网穿透配置
```toml
[[proxies]]
name = "QR-code-added-gem12-tcp"
type = "tcp"
localIP = "127.0.0.1"
localPort = 8080
remotePort = 18080

[[proxies]]
name = "QR-code-verification-gem12-tcp"
type = "tcp"
localIP = "127.0.0.1"
localPort = 8081
remotePort = 18081
```

#### 反向代理配置
- **上传服务**: https://qr.gaoliming.top → http://************:18080
- **验证服务**: https://qrverification.nanye.site → http://************:18081

### 服务配置更新

已将验证服务URL更新为云端地址：
- **原配置**: `http://localhost:8081`
- **新配置**: `https://qrverification.nanye.site`

## 🚀 部署方式

### 方式一：生产环境部署（推荐）

使用云端验证服务地址：

```bash
# 使用生产环境配置启动
docker compose -f docker-compose.prod.yml up -d

# 或者使用默认配置（已更新为生产环境）
docker compose up -d
```

### 方式二：开发环境部署

使用本地验证服务地址：

```bash
# 使用开发环境配置启动
docker compose -f docker-compose.dev.yml up -d
```

### 方式三：自定义环境变量

```bash
# 临时设置环境变量
export VERIFY_SERVICE_BASE_URL=https://qrverification.nanye.site
docker compose up -d

# 或者在启动时指定
VERIFY_SERVICE_BASE_URL=https://qrverification.nanye.site docker compose up -d
```

## 📋 配置文件说明

### docker-compose.yml（默认/生产环境）
- 验证服务URL: `https://qrverification.nanye.site`
- 适用于生产环境和云端部署

### docker-compose.prod.yml（生产环境）
- 验证服务URL: `https://qrverification.nanye.site`
- 明确标识为生产环境配置

### docker-compose.dev.yml（开发环境）
- 验证服务URL: `http://localhost:8081`
- 适用于本地开发和测试

## 🔧 服务访问地址

### 生产环境（云端）
- **上传页面**: https://qr.gaoliming.top
- **验证服务**: https://qrverification.nanye.site/{image_id}
- **本地管理**: http://localhost:8080（通过FRP穿透）

### 开发环境（本地）
- **上传页面**: http://localhost:8080
- **验证服务**: http://localhost:8081/{image_id}

## 🧪 测试验证

### 1. 服务状态检查
```bash
# 检查容器状态
docker compose ps

# 检查服务日志
docker compose logs
```

### 2. 功能测试
```bash
# 运行测试脚本
python3 test_service.py

# 运行云端测试脚本（如果需要）
python3 test_cloud_service.py
```

### 3. 手动测试流程
1. 访问上传页面：https://qr.gaoliming.top
2. 上传测试图片
3. 下载处理后的图片
4. 用手机扫描二维码
5. 验证是否跳转到：https://qrverification.nanye.site/{image_id}

## 🔄 配置切换

### 从开发环境切换到生产环境
```bash
# 停止当前服务
docker compose down

# 启动生产环境服务
docker compose -f docker-compose.prod.yml up -d
```

### 从生产环境切换到开发环境
```bash
# 停止当前服务
docker compose down

# 启动开发环境服务
docker compose -f docker-compose.dev.yml up -d
```

## 📊 监控和维护

### 日志查看
```bash
# 查看所有服务日志
docker compose logs

# 查看特定服务日志
docker compose logs upload-service
docker compose logs verify-service

# 实时查看日志
docker compose logs -f
```

### 服务重启
```bash
# 重启所有服务
docker compose restart

# 重启特定服务
docker compose restart upload-service
```

### 数据备份
```bash
# 备份处理后的图片
tar -czf qr_images_backup_$(date +%Y%m%d).tar.gz data/processed/
```

## 🛠️ 故障排除

### 常见问题

1. **二维码指向错误地址**
   - 检查环境变量 `VERIFY_SERVICE_BASE_URL`
   - 重新构建并启动服务

2. **云端服务无法访问**
   - 检查FRP连接状态
   - 验证反向代理配置
   - 检查防火墙设置

3. **图片处理失败**
   - 检查磁盘空间
   - 查看服务日志
   - 验证文件权限

### 调试命令
```bash
# 检查环境变量
docker compose exec upload-service env | grep VERIFY

# 进入容器调试
docker compose exec upload-service bash

# 检查网络连接
docker compose exec upload-service curl https://qrverification.nanye.site
```

## 🔒 安全建议

1. **HTTPS配置**: 确保所有外部访问都使用HTTPS
2. **防火墙设置**: 限制不必要的端口访问
3. **定期备份**: 定期备份重要数据
4. **日志监控**: 监控异常访问和错误日志
5. **版本更新**: 定期更新Docker镜像和依赖

## 📈 性能优化

1. **资源限制**: 在docker-compose.yml中添加资源限制
2. **缓存策略**: 配置适当的HTTP缓存头
3. **负载均衡**: 如需要可配置多实例负载均衡
4. **CDN加速**: 考虑为静态资源配置CDN

## 🎯 下一步建议

1. 配置监控告警系统
2. 设置自动备份策略
3. 添加服务健康检查
4. 考虑容器编排（如Kubernetes）
5. 实现CI/CD自动部署
