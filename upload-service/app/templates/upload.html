<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码图片处理服务</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .upload-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
        }
        
        .preview-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
        }
        
        .section-title {
            font-size: 1.3em;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .file-upload {
            border: 3px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-upload:hover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.05);
        }
        
        .file-upload.dragover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.1);
        }
        
        .upload-icon {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .upload-text {
            font-size: 1.1em;
            color: #666;
            margin-bottom: 15px;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .controls {
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .control-group label {
            min-width: 100px;
            font-weight: 500;
            color: #333;
        }
        
        .control-group input {
            flex: 1;
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1em;
        }
        
        .control-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .preview-container {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            background: #fff;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 400px;
            object-fit: contain;
        }
        
        .qr-preview {
            position: absolute;
            border: 2px solid #ff4757;
            background: rgba(255, 71, 87, 0.1);
            pointer-events: none;
        }
        
        .no-preview {
            color: #999;
            font-size: 1.1em;
        }
        
        .file-list {
            margin-top: 15px;
        }
        
        .file-item {
            background: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .file-name {
            font-weight: 500;
            color: #333;
        }
        
        .file-size {
            color: #666;
            font-size: 0.9em;
        }
        
        .process-btn {
            width: 100%;
            margin-top: 20px;
            padding: 15px;
            font-size: 1.1em;
            font-weight: 600;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>二维码图片处理服务</h1>
            <p>上传图片，自动添加二维码，支持批量处理</p>
        </div>
        
        <div class="content">
            <div class="upload-section">
                <h2 class="section-title">📁 文件上传</h2>
                
                <div class="file-upload" id="fileUpload">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">点击选择图片或拖拽到此处</div>
                    <div class="upload-text">支持 JPG、PNG、WEBP、BMP 等格式</div>
                    <input type="file" id="fileInput" class="file-input" multiple accept="image/*">
                    <button type="button" class="btn" onclick="document.getElementById('fileInput').click()">
                        选择文件
                    </button>
                </div>
                
                <div class="file-list" id="fileList"></div>
                
                <h3 class="section-title">⚙️ 参数设置</h3>
                <div class="controls">
                    <div class="control-group">
                        <label for="marginLeft">左边距:</label>
                        <input type="number" id="marginLeft" value="20" min="0" max="500">
                        <span>px</span>
                    </div>
                    <div class="control-group">
                        <label for="marginBottom">下边距:</label>
                        <input type="number" id="marginBottom" value="20" min="0" max="500">
                        <span>px</span>
                    </div>
                    <div class="control-group">
                        <label for="qrSizeRatio">二维码尺寸:</label>
                        <input type="number" id="qrSizeRatio" value="0.15" min="0.01" max="1.0" step="0.01">
                        <span>比例</span>
                    </div>
                </div>
                
                <button type="button" class="btn process-btn" id="processBtn" onclick="processImages()">
                    开始处理
                </button>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <div>正在处理图片，请稍候...</div>
                </div>
            </div>
            
            <div class="preview-section">
                <h2 class="section-title">👁️ 实时预览</h2>
                <div class="preview-container" id="previewContainer">
                    <div class="no-preview">请先选择图片文件</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let previewImage = null;

        // 文件上传相关
        const fileUpload = document.getElementById('fileUpload');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');
        const previewContainer = document.getElementById('previewContainer');

        // 参数控件
        const marginLeft = document.getElementById('marginLeft');
        const marginBottom = document.getElementById('marginBottom');
        const qrSizeRatio = document.getElementById('qrSizeRatio');

        // 拖拽上传
        fileUpload.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUpload.classList.add('dragover');
        });

        fileUpload.addEventListener('dragleave', () => {
            fileUpload.classList.remove('dragover');
        });

        fileUpload.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUpload.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        });

        // 文件选择
        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            handleFiles(files);
        });

        // 处理文件
        function handleFiles(files) {
            selectedFiles = files.filter(file => file.type.startsWith('image/'));
            updateFileList();
            updatePreview();
        }

        // 更新文件列表
        function updateFileList() {
            fileList.innerHTML = '';
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">${(file.size / 1024 / 1024).toFixed(2)} MB</span>
                `;
                fileList.appendChild(fileItem);
            });
        }

        // 更新预览
        function updatePreview() {
            if (selectedFiles.length === 0) {
                previewContainer.innerHTML = '<div class="no-preview">请先选择图片文件</div>';
                return;
            }

            const file = selectedFiles[0]; // 预览第一张图片
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = new Image();
                img.onload = () => {
                    previewContainer.innerHTML = '';
                    
                    const imgElement = document.createElement('img');
                    imgElement.src = e.target.result;
                    imgElement.className = 'preview-image';
                    previewContainer.appendChild(imgElement);
                    
                    previewImage = img;
                    updateQRPreview();
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        // 更新二维码预览
        function updateQRPreview() {
            if (!previewImage) return;

            // 移除现有的二维码预览
            const existingQR = previewContainer.querySelector('.qr-preview');
            if (existingQR) {
                existingQR.remove();
            }

            const imgElement = previewContainer.querySelector('.preview-image');
            if (!imgElement) return;

            // 获取参数
            const leftMargin = parseInt(marginLeft.value) || 20;
            const bottomMargin = parseInt(marginBottom.value) || 20;
            const sizeRatio = parseFloat(qrSizeRatio.value) || 0.15;

            // 计算预览尺寸比例
            const imgRect = imgElement.getBoundingClientRect();
            const containerRect = previewContainer.getBoundingClientRect();
            
            const scaleX = imgRect.width / previewImage.width;
            const scaleY = imgRect.height / previewImage.height;
            const scale = Math.min(scaleX, scaleY);

            // 计算二维码尺寸和位置
            const qrSize = previewImage.width * sizeRatio * scale;
            const qrLeft = leftMargin * scale;
            const qrBottom = bottomMargin * scale;

            // 创建二维码预览框
            const qrPreview = document.createElement('div');
            qrPreview.className = 'qr-preview';
            qrPreview.style.width = qrSize + 'px';
            qrPreview.style.height = qrSize + 'px';
            qrPreview.style.left = (imgRect.left - containerRect.left + qrLeft) + 'px';
            qrPreview.style.bottom = (containerRect.bottom - imgRect.bottom + qrBottom) + 'px';

            previewContainer.appendChild(qrPreview);
        }

        // 参数变化时更新预览
        [marginLeft, marginBottom, qrSizeRatio].forEach(input => {
            input.addEventListener('input', updateQRPreview);
        });

        // 处理图片
        async function processImages() {
            if (selectedFiles.length === 0) {
                alert('请先选择图片文件');
                return;
            }

            const processBtn = document.getElementById('processBtn');
            const loading = document.getElementById('loading');

            processBtn.style.display = 'none';
            loading.style.display = 'block';

            try {
                const formData = new FormData();
                
                selectedFiles.forEach(file => {
                    formData.append('files', file);
                });
                
                formData.append('marginLeft', marginLeft.value);
                formData.append('marginBottom', marginBottom.value);
                formData.append('qrSizeRatio', qrSizeRatio.value);

                const response = await fetch('/api/generate', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`处理失败: ${response.statusText}`);
                }

                // 下载文件
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                
                // 根据文件数量设置文件名
                if (selectedFiles.length === 1) {
                    const originalName = selectedFiles[0].name.split('.')[0];
                    a.download = `processed_${originalName}.jpg`;
                } else {
                    const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '_');
                    a.download = `processed_images_${timestamp}.zip`;
                }
                
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

            } catch (error) {
                alert('处理失败: ' + error.message);
            } finally {
                processBtn.style.display = 'block';
                loading.style.display = 'none';
            }
        }

        // 窗口大小变化时更新预览
        window.addEventListener('resize', updateQRPreview);
    </script>
</body>
</html>
