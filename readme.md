# 二维码添加与验证网页服务 - 需求与技术规格文档

#### **1. 项目概述**

本项目旨在开发一个轻量级的网页服务，允许用户上传图片，在图片的指定位置添加一个包含唯一验证链接的二维码，并下载处理后的图片。通过扫描图片上的二维码，可以访问一个验证页面，该页面仅用于展示这张添加了二维码的图片，以达到验证的目的。整个服务将通过 Docker 进行容器化部署，确保环境一致性和部署便捷性。

#### **2. 功能需求 (Functional Requirements)**

  * **FR-1: 图片上传**

      * **FR-1.1 (单张上传)**: 用户可以上传单张图片。
      * **FR-1.2 (批量上传)**: 用户可以一次性选择并上传多张图片。
      * **FR-1.3 (格式支持)**: 无需限制上传图片的格式，后端应能处理主流图片格式 (如 JPG, PNG, WEBP, BMP 等)。

  * **FR-2: 二维码自定义**

      * **FR-2.1 (位置自定义)**: 用户可以自定义二维码在图片上的位置，通过设置其距离图片**左边缘**和**下边缘**的像素值（边距）来实现。
      * **FR-2.2 (尺寸自定义)**: 用户可以自定义二维码的大小，通过设置一个相对于图片**宽度**的**比例值**（例如，15%）来实现。

  * **FR-3: 图片处理与生成**

      * **FR-3.1 (ID 生成)**: 为每张成功处理的图片生成一个全局唯一的 **UUID** 作为其唯一标识。
      * **FR-3.2 (二维码内容)**: 二维码内容为一个URL，格式为 `http://<服务器IP>:<验证服务端口>/<UUID>`。
      * **FR-3.3 (输出格式)**: 所有处理完成的图片，无论原始格式如何，统一保存并输出为 **JPG** 格式。
      * **FR-3.4 (处理逻辑)**: 系统将根据用户设定的位置和尺寸参数，将生成的二维码直接叠加在原始图片上。

  * **FR-4: 结果下载**

      * **FR-4.1 (单张下载)**: 如果用户只上传了一张图片，处理完成后，网页直接提供该张图片的下载链接。
      * **FR-4.2 (批量下载)**: 如果用户上传了多张图片，处理完成后，网页提供一个 **ZIP 压缩包** 的下载链接，其中包含所有成功处理的图片。
      * **FR-4.3 (ZIP命名)**: 下载的 ZIP 文件名采用 `processed_images_YYYYMMDD_HHMMSS.zip` 的格式。

  * **FR-5: 失败处理**

      * **FR-5.1 (批量任务)**: 在批量上传任务中，若有部分图片因任何原因处理失败，系统应忽略这些失败的图片，继续完成其余图片的任务，并只将成功的图片打包。

  * **FR-6: 扫码验证**

      * **FR-6.1 (验证页面)**: 通过手机等设备扫描图片上的二维码，会打开一个网页。
      * **FR-6.2 (页面内容)**: 该网页只显示对应的、已添加二维码的图片，不包含任何其他文字、按钮或装饰。
      * **FR-6.3 (无效链接)**: 如果访问的 UUID 链接不存在，页面应返回标准的 404 Not Found 错误。

#### **3. 非功能性需求 (Non-Functional Requirements)**

  * **NFR-1: 部署**

      * 整个应用（包括上传和验证服务）必须通过 **Docker** 进行部署。
      * 应提供 `docker-compose.yml` 文件以实现一键部署。

  * **NFR-2: 架构**

      * **服务独立**: 图片上传处理服务和扫码验证服务必须是两个**独立运行**的容器。
      * **高可用性**: 上传服务的崩溃或重启，不得影响已生成二维码的验证服务的正常运行。

  * **NFR-3: 用户体验 (UX)**

      * **实时预览**: 前端界面必须提供**实时预览**功能。用户在调整二维码的位置和尺寸参数时，能在上传的图片上看到一个预览框实时变化，以获得所见即所得的体验。
      * **单次设置**: 用户在页面上进行的参数设置仅对当次上传任务有效，无需在浏览器中持久化。

  * **NFR-4: 安全性**

      * 无需用户登录或任何身份验证机制，服务对所有可访问其IP与端口的用户开放。

#### **4. 技术规格 (Technical Specifications)**

  * **TS-1: 核心技术栈**

      * **后端**: Python 3.x + **FastAPI**
      * **图片处理库**: **Pillow** (PIL Fork)
      * **前端**: HTML, CSS, JavaScript (无特定框架要求)

  * **TS-2: 图片处理细节**

      * **JPG 输出质量**: 为保证视觉效果，所有生成的 JPG 图片统一采用 **95** 的高质量参数。
      * **二维码样式**: 纯黑色二维码，不含任何边框或 Logo。

  * **TS-3: 数据存储**

      * **存储方式**: 通过 Docker 卷映射（Volume Mapping）将数据持久化到宿主机本地文件夹。
      * **目录结构**:
        ```
        /data/
        ├── originals/   # (可选) 存放用户上传的原始图片，便于追溯
        └── processed/   # 存放添加了二维码的图片，文件名为 <UUID>.jpg
        ```

  * **TS-4: API 端点草案**

      * **上传服务 (`upload-service`)**:
          * `POST /api/generate`
              * **请求体**: `multipart/form-data`
                  * `files`: 图片文件列表
                  * `marginLeft`: 左边距 (px)
                  * `marginBottom`: 下边距 (px)
                  * `qrSizeRatio`: 二维码尺寸比例 (0.01 - 1.0)
              * **成功响应**: 返回处理后的单张图片文件或 ZIP 压缩包。
      * **验证服务 (`verify-service`)**:
          * `GET /{image_id:uuid}`
              * **路径参数**: `image_id` - 图片的 UUID。
              * **成功响应**: 返回 `image/jpeg` 内容。

#### **5. Docker 部署方案**

  * **项目文件结构**:

    ```
    qrcode_service/
    ├── docker-compose.yml
    ├── data/
    │   └── processed/
    ├── upload-service/
    │   ├── Dockerfile
    │   ├── requirements.txt
    │   └── app/
    │       └── main.py
    └── verify-service/
        ├── Dockerfile
        ├── requirements.txt
        └── app/
            └── main.py
    ```

  * **`docker-compose.yml`**:

    ```yaml
    version: '3.8'
    
    services:
      # 服务一：负责上传和处理图片
      upload-service:
        build: ./upload-service
        ports:
          # 将宿主机的 8080 端口映射到容器，用于访问上传页面
          - "8080:80"
        volumes:
          # 将 data 目录映射到容器内，用于存储图片
          - ./data:/app/data
        environment:
          # 告知上传服务，验证服务的访问地址是什么
          - VERIFY_SERVICE_BASE_URL=http://<你的服务器IP>:8081
    
      # 服务二：负责扫码后的图片验证和展示
      verify-service:
        build: ./verify-service
        ports:
          # 将宿主机的 8081 端口映射到容器，用于扫码验证
          - "8081:80"
        volumes:
          # 只将处理好的图片目录映射进去，权限最小化
          - ./data/processed:/app/static/images
    
    ```

    **注意**: 在使用前，请将 `<你的服务器IP>` 替换为您的实际服务器内网或公网 IP 地址。

-----

这份文档已经非常详尽，完全可以作为您个人开发的指导蓝图。祝您开发顺利！