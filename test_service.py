#!/usr/bin/env python3
"""
二维码服务测试脚本
用于测试上传服务和验证服务的基本功能
"""

import requests
import json
import time
from PIL import Image, ImageDraw
import io
import os

def create_test_image(width=800, height=600, color='lightblue', text='Test Image'):
    """创建测试图片"""
    img = Image.new('RGB', (width, height), color)
    draw = ImageDraw.Draw(img)
    
    # 添加文字
    text_bbox = draw.textbbox((0, 0), text)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    draw.text((x, y), text, fill='black')
    
    return img

def test_upload_service():
    """测试上传服务"""
    print("🧪 测试上传服务...")
    
    # 创建测试图片
    test_img = create_test_image()
    
    # 将图片转换为字节流
    img_bytes = io.BytesIO()
    test_img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    # 准备上传数据
    files = {
        'files': ('test_image.png', img_bytes, 'image/png')
    }
    
    data = {
        'marginLeft': 30,
        'marginBottom': 30,
        'qrSizeRatio': 0.2
    }
    
    try:
        # 发送请求
        response = requests.post(
            'http://localhost:8080/api/generate',
            files=files,
            data=data,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 上传服务测试成功")
            
            # 保存处理后的图片
            with open('test_output.jpg', 'wb') as f:
                f.write(response.content)
            print("📁 处理后的图片已保存为 test_output.jpg")
            
            return True
        else:
            print(f"❌ 上传服务测试失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 上传服务连接失败: {e}")
        return False

def test_verify_service():
    """测试验证服务健康检查"""
    print("🧪 测试验证服务...")
    
    try:
        # 测试健康检查端点（如果存在）
        response = requests.get('http://localhost:8081/health', timeout=10)
        
        if response.status_code == 200:
            print("✅ 验证服务健康检查成功")
            return True
        else:
            print(f"⚠️  验证服务响应: {response.status_code}")
            # 验证服务可能没有健康检查端点，这是正常的
            return True
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 验证服务连接失败: {e}")
        return False

def test_upload_page():
    """测试上传页面"""
    print("🧪 测试上传页面...")
    
    try:
        response = requests.get('http://localhost:8080/', timeout=10)
        
        if response.status_code == 200:
            print("✅ 上传页面访问成功")
            return True
        else:
            print(f"❌ 上传页面访问失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 上传页面连接失败: {e}")
        return False

def check_services_running():
    """检查服务是否运行"""
    print("🔍 检查服务状态...")
    
    services = [
        ('上传服务', 'http://localhost:8080/'),
        ('验证服务', 'http://localhost:8081/')
    ]
    
    all_running = True
    
    for name, url in services:
        try:
            response = requests.get(url, timeout=5)
            print(f"✅ {name} 运行正常 (端口: {url.split(':')[-1].split('/')[0]})")
        except requests.exceptions.RequestException:
            print(f"❌ {name} 无法访问")
            all_running = False
    
    return all_running

def main():
    """主测试函数"""
    print("🚀 开始测试二维码服务...")
    print("=" * 50)
    
    # 检查服务状态
    if not check_services_running():
        print("\n❌ 部分服务未运行，请先启动服务：")
        print("   docker compose up -d")
        return
    
    print("\n" + "=" * 50)
    
    # 测试上传页面
    test_upload_page()
    
    print("\n" + "=" * 50)
    
    # 测试验证服务
    test_verify_service()
    
    print("\n" + "=" * 50)
    
    # 测试上传服务
    test_upload_service()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    
    if os.path.exists('test_output.jpg'):
        print("\n📋 测试结果:")
        print("   - 生成了测试图片: test_output.jpg")
        print("   - 请检查图片左下角是否有二维码")
        print("   - 可以用手机扫描二维码测试验证功能")

if __name__ == "__main__":
    main()
