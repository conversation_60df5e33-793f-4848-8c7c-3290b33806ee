# 二维码添加与验证网页服务

一个基于Docker的二维码添加和扫码验证网页服务，支持图片上传、二维码添加、批量处理和扫码验证功能。

## ✨ 功能特性

- 🖼️ **图片上传**: 支持单张/批量上传多种格式图片
- 🔲 **二维码添加**: 自动在图片左下角添加纯黑色二维码
- 👁️ **实时预览**: 调整参数时可实时预览二维码位置和尺寸
- ⚙️ **参数自定义**: 可设置二维码距离边缘的距离和尺寸比例
- 📦 **批量处理**: 支持多文件上传和ZIP打包下载
- 📱 **扫码验证**: 扫描二维码可打开验证页面查看原图
- 🐳 **Docker部署**: 完整的容器化解决方案
- 🔄 **服务独立**: 上传服务和验证服务独立运行

## 🚀 快速开始

### 启动服务

```bash
docker compose up -d
```

### 访问服务

- **上传页面**: <http://localhost:8080>
- **验证服务**: <http://localhost:8081/{image_id}>

## 📁 项目结构

```text
qr-code-service/
├── docker-compose.yml          # Docker容器编排配置
├── data/                       # 数据存储目录
│   └── processed/              # 处理后的图片
├── upload-service/             # 上传服务
│   ├── Dockerfile
│   ├── requirements.txt
│   └── app/
│       ├── main.py            # 上传服务主程序
│       └── templates/
│           └── upload.html    # 上传页面(含实时预览)
└── verify-service/            # 验证服务
    ├── Dockerfile
    ├── requirements.txt
    └── app/
        ├── main.py           # 验证服务主程序
        └── templates/
            └── view.html     # 验证页面
```

## 🔧 部署方式

### 首次部署

```bash
# 启动所有服务
docker compose up -d
```

### 修改代码后重新部署

当您修改了代码后，需要重新构建容器镜像并部署：

```bash
# 1. 停止当前运行的容器
docker compose down

# 2. 重新构建镜像（强制重新构建，不使用缓存）
docker compose build --no-cache

# 3. 启动新的容器
docker compose up -d

# 或者一步完成（推荐）
docker compose up -d --build --force-recreate
```

### 清理旧镜像（可选）

```bash
# 清理未使用的镜像以释放磁盘空间
docker image prune -f

# 或者清理所有未使用的资源
docker system prune -f
```

### 查看服务状态

```bash
# 查看容器运行状态
docker compose ps

# 查看服务日志
docker compose logs -f

# 查看特定服务日志
docker compose logs -f upload-service
docker compose logs -f verify-service
```

## 📖 详细文档

- [使用说明](使用说明.md) - 详细的使用指南和功能说明
- [部署指南](部署指南.md) - 完整的部署和运维指南
- [原始需求文档](原始需求文档.md) - 项目的原始需求和技术规格

## 🛠️ 技术栈

- **后端**: FastAPI + Python 3.11
- **图片处理**: Pillow (PIL)
- **二维码生成**: qrcode
- **前端**: HTML + CSS + JavaScript
- **容器化**: Docker + Docker Compose

## 🌐 本地部署

项目使用Docker Compose进行本地容器化部署：

- 上传服务: <http://localhost:8080>
- 验证服务: <http://localhost:8081>

## 📝 使用流程

1. 访问上传页面
2. 选择图片文件（支持拖拽）
3. 调整二维码参数（实时预览）
4. 处理并下载图片
5. 扫描二维码验证

## 🔒 安全特性

- HTTPS加密传输
- 文件类型验证
- 容器隔离
- UUID唯一标识

## 📊 性能特点

- 单张图片处理时间 < 2秒
- 支持多用户并发
- 高质量JPG输出
- 内存优化处理

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

请根据实际情况添加适当的许可证。
