version: '3.8'

services:
  # 服务一：负责上传和处理图片
  upload-service:
    build: ./upload-service
    ports:
      # 将宿主机的 8080 端口映射到容器，用于访问上传页面
      - "8080:80"
    volumes:
      # 将 data 目录映射到容器内，用于存储图片
      - ./data:/app/data
    environment:
      # 生产环境：使用云端验证服务地址
      - VERIFY_SERVICE_BASE_URL=https://qrverification.nanye.site
    depends_on:
      - verify-service

  # 服务二：负责扫码后的图片验证和展示
  verify-service:
    build: ./verify-service
    ports:
      # 将宿主机的 8081 端口映射到容器，用于扫码验证
      - "8081:80"
    volumes:
      # 只将处理好的图片目录映射进去，权限最小化
      - ./data/processed:/app/static/images
