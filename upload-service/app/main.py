import os
import uuid
import zipfile
from datetime import datetime
from typing import List, Optional
from io import BytesIO

from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import FileResponse, StreamingResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request
import aiofiles
from PIL import Image, ImageDraw
import qrcode
import magic

app = FastAPI(title="QR Code Upload Service")

# 配置模板
templates = Jinja2Templates(directory="app/templates")

# 环境变量
VERIFY_SERVICE_BASE_URL = os.getenv("VERIFY_SERVICE_BASE_URL", "http://localhost:8081")
DATA_DIR = "/app/data"
PROCESSED_DIR = f"{DATA_DIR}/processed"

# 确保目录存在
os.makedirs(PROCESSED_DIR, exist_ok=True)

# 支持的图片格式
SUPPORTED_FORMATS = {
    'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 
    'image/bmp', 'image/tiff', 'image/gif'
}

def generate_qr_code(image_id: str, size: int) -> Image.Image:
    """生成纯黑色二维码"""
    qr_url = f"{VERIFY_SERVICE_BASE_URL}/{image_id}"
    
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=0,  # 无边框
    )
    qr.add_data(qr_url)
    qr.make(fit=True)
    
    # 生成纯黑色二维码
    qr_img = qr.make_image(fill_color="black", back_color="white")
    
    # 调整尺寸
    qr_img = qr_img.resize((size, size), Image.Resampling.LANCZOS)
    
    return qr_img

def add_qr_to_image(original_img: Image.Image, qr_img: Image.Image, 
                   margin_left: int, margin_bottom: int) -> Image.Image:
    """在图片上添加二维码"""
    # 转换为RGB模式以确保JPG输出
    if original_img.mode != 'RGB':
        original_img = original_img.convert('RGB')
    
    img_copy = original_img.copy()
    img_width, img_height = img_copy.size
    qr_width, qr_height = qr_img.size
    
    # 计算位置（左下角）
    paste_x = margin_left
    paste_y = img_height - qr_height - margin_bottom
    
    # 确保二维码不会超出图片边界
    if paste_x + qr_width > img_width or paste_y < 0:
        raise ValueError("二维码位置超出图片边界")
    
    # 粘贴二维码
    img_copy.paste(qr_img, (paste_x, paste_y))
    
    return img_copy

async def process_single_image(file: UploadFile, margin_left: int,
                             margin_bottom: int, qr_size_ratio: float) -> str:
    """处理单张图片"""
    # 验证文件类型
    file_content = await file.read()
    file_type = magic.from_buffer(file_content, mime=True)

    if file_type not in SUPPORTED_FORMATS:
        raise HTTPException(status_code=400, detail=f"不支持的文件格式: {file_type}")

    # 生成唯一ID
    image_id = str(uuid.uuid4())

    try:
        # 打开图片
        original_img = Image.open(BytesIO(file_content))

        # 计算二维码尺寸（基于图片宽度的比例）
        qr_size = int(original_img.width * qr_size_ratio)
        if qr_size < 10:  # 最小尺寸限制
            qr_size = 10

        # 生成二维码
        qr_img = generate_qr_code(image_id, qr_size)

        # 添加二维码到图片
        processed_img = add_qr_to_image(original_img, qr_img, margin_left, margin_bottom)

        # 保存处理后的图片（JPG格式，质量95）
        output_path = f"{PROCESSED_DIR}/{image_id}.jpg"
        processed_img.save(output_path, "JPEG", quality=95)

        return image_id

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"图片处理失败: {str(e)}")

@app.get("/", response_class=HTMLResponse)
async def upload_page(request: Request):
    """上传页面"""
    return templates.TemplateResponse("upload.html", {"request": request})

@app.post("/api/generate")
async def generate_qr_images(
    files: List[UploadFile] = File(...),
    marginLeft: int = Form(20),
    marginBottom: int = Form(20),
    qrSizeRatio: float = Form(0.15)
):
    """处理图片并添加二维码"""
    
    # 验证参数
    if not (0.01 <= qrSizeRatio <= 1.0):
        raise HTTPException(status_code=400, detail="二维码尺寸比例必须在0.01-1.0之间")

    if marginLeft < 0 or marginBottom < 0:
        raise HTTPException(status_code=400, detail="边距不能为负数")

    processed_ids = []
    failed_files = []

    # 处理每个文件
    for file in files:
        try:
            image_id = await process_single_image(file, marginLeft, marginBottom, qrSizeRatio)
            processed_ids.append(image_id)
        except Exception as e:
            failed_files.append(f"{file.filename}: {str(e)}")

    if not processed_ids:
        raise HTTPException(status_code=400, detail="没有成功处理的图片")
    
    # 单张图片直接返回
    if len(processed_ids) == 1:
        image_path = f"{PROCESSED_DIR}/{processed_ids[0]}.jpg"
        return FileResponse(
            image_path, 
            media_type="image/jpeg",
            filename=f"processed_{files[0].filename.rsplit('.', 1)[0]}.jpg"
        )
    
    # 多张图片打包为ZIP
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = f"processed_images_{timestamp}.zip"
    
    zip_buffer = BytesIO()
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for i, image_id in enumerate(processed_ids):
            image_path = f"{PROCESSED_DIR}/{image_id}.jpg"
            original_name = files[i].filename.rsplit('.', 1)[0]
            zip_file.write(image_path, f"processed_{original_name}.jpg")
    
    zip_buffer.seek(0)
    
    return StreamingResponse(
        BytesIO(zip_buffer.read()),
        media_type="application/zip",
        headers={"Content-Disposition": f"attachment; filename={zip_filename}"}
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=80)
