FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libmagic1 \
    && rm -rf /var/lib/apt/lists/*

# 复制并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY app/ ./app/

# 创建数据目录
RUN mkdir -p /app/data/processed /app/data/original

# 暴露端口
EXPOSE 80

# 启动应用
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "80"]
